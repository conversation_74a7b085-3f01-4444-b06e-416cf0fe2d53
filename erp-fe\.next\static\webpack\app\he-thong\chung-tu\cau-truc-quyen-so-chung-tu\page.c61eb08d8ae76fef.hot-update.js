"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx":
/*!************************************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx ***!
  \************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getInputTableColumns: function() { return /* binding */ getInputTableColumns; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/search-columns */ \"(app-pages-browser)/./src/constants/search-columns/index.tsx\");\n/* harmony import */ var _components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito/custom-input-table/components */ \"(app-pages-browser)/./src/components/custom/arito/custom-input-table/components/index.ts\");\n/* harmony import */ var _components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom/arito/form/custom-search-field */ \"(app-pages-browser)/./src/components/custom/arito/form/custom-search-field/index.tsx\");\n/* harmony import */ var _constants_query_keys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/query-keys */ \"(app-pages-browser)/./src/constants/query-keys.ts\");\n\n\n\n\n\n\nconst getInputTableColumns = (param)=>{\n    let { onCellValueChange, onChungTuSelect, onUserSelect } = param;\n    return [\n        {\n            field: \"ma_ct\",\n            headerName: \"M\\xe3 chứng từ\",\n            width: 175,\n            renderCell: (params)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__.SearchField, {\n                    value: params.row.ma_ct || \"\",\n                    onValueChange: (newValue)=>{\n                        var _newValue_target;\n                        var _newValue_target_value;\n                        const normalizedValue = typeof newValue === \"string\" ? newValue : (_newValue_target_value = newValue === null || newValue === void 0 ? void 0 : (_newValue_target = newValue.target) === null || _newValue_target === void 0 ? void 0 : _newValue_target.value) !== null && _newValue_target_value !== void 0 ? _newValue_target_value : \"\";\n                        onCellValueChange(params.row.uuid, \"ma_ct\", normalizedValue);\n                    },\n                    searchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].CHUNG_TU),\n                    searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__.chungTuSearchColumns,\n                    dialogTitle: \"Danh mục chứng từ\",\n                    className: \"w-full\",\n                    columnDisplay: \"ma_ct\",\n                    onRowSelection: (selectedChungTu)=>{\n                        if (selectedChungTu) {\n                            onCellValueChange(params.row.uuid, \"ma_ct\", selectedChungTu.ma_ct);\n                            onCellValueChange(params.row.uuid, \"ten_ct\", selectedChungTu.ten_ct || \"\");\n                            if (onChungTuSelect) {\n                                onChungTuSelect(params.row.uuid, selectedChungTu);\n                            }\n                        }\n                    }\n                }, \"\".concat(params.row.uuid, \"-\").concat(params.row.ma_ct || \"\"), false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"ten_ct\",\n            headerName: \"T\\xean chứng từ\",\n            width: 200,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"ten_ct\",\n                    type: \"text\",\n                    value: params.row.ten_ct,\n                    onValueChange: (newValue)=>onCellValueChange(params.row.uuid, \"ten_ct\", newValue),\n                    disabled: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 7\n                }, undefined)\n        },\n        {\n            field: \"username\",\n            headerName: \"Người sử dụng Nh\\xf3m\",\n            width: 175,\n            renderCell: (params)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__.SearchField, {\n                    value: params.row.username || \"\",\n                    onValueChange: (newValue)=>{\n                        var _newValue_target;\n                        var _newValue_target_value;\n                        const normalizedValue = typeof newValue === \"string\" ? newValue : (_newValue_target_value = newValue === null || newValue === void 0 ? void 0 : (_newValue_target = newValue.target) === null || _newValue_target === void 0 ? void 0 : _newValue_target.value) !== null && _newValue_target_value !== void 0 ? _newValue_target_value : \"\";\n                        onCellValueChange(params.row.uuid, \"username\", normalizedValue);\n                        if (!normalizedValue) {\n                            onCellValueChange(params.row.uuid, \"first_name\", \"\");\n                        }\n                    },\n                    otherSearchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].NGUOI_SU_DUNG),\n                    searchEndpoint: \"\",\n                    searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__.userSearchColumns,\n                    dialogTitle: \"Danh mục người sử dụng\",\n                    className: \"w-full\",\n                    displayRelatedField: \"first_name\",\n                    columnDisplay: \"username\",\n                    relatedFieldValue: params.row.first_name || \"\",\n                    onRowSelection: (selectedUser)=>{\n                        if (selectedUser) {\n                            onCellValueChange(params.row.uuid, \"username\", selectedUser.username);\n                            onCellValueChange(params.row.uuid, \"first_name\", selectedUser.first_name);\n                            // Call parent callback for additional processing\n                            if (onUserSelect) {\n                                onUserSelect(params.row.uuid, selectedUser);\n                            }\n                        }\n                    }\n                }, \"\".concat(params.row.uuid, \"-\").concat(params.row.username || \"\"), false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"first_name\",\n            headerName: \"T\\xean người sử dụng\",\n            width: 200,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"first_name\",\n                    type: \"text\",\n                    value: params.row.first_name,\n                    onValueChange: (newValue)=>onCellValueChange(params.row.uuid, \"first_name\", newValue),\n                    disabled: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 7\n                }, undefined)\n        }\n    ];\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx\n"));

/***/ })

});